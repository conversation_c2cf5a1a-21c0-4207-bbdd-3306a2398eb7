package supportsvc

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"go.starlark.net/starlark"
)

// runStarlarkTransform chạy code Starlark do client gửi.
// Yêu cầu script định nghĩa: def run(rows): ...; return <any JSON-serializable>
func runStarlarkTransform(ctx context.Context, code string, rows []map[string]any, timeout time.Duration) (any, error) {
	// chuẩn bị thread (sandbox, no import)
	thread := &starlark.Thread{
		Name: "db-script",
		Print: func(_ *starlark.Thread, msg string) {
			// bỏ qua hoặc log nếu muốn
			_ = msg
		},
		// Load = nil => cấm "load(...)"
	}

	// Hỗ trợ cancel theo context
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	done := make(chan struct{})
	var runErr error
	var result any

	go func() {
		defer close(done)

		// predeclared builtins (an toàn)
		pre := starlark.StringDict{
			// helpers
			"time_format":   starlark.NewBuiltin("time_format", bltnTimeFormat),
			"to_float":      starlark.NewBuiltin("to_float", bltnToFloat),
			"to_int":        starlark.NewBuiltin("to_int", bltnToInt),
			"to_str":        starlark.NewBuiltin("to_str", bltnToStr),
			"epoch_ms":      starlark.NewBuiltin("epoch_ms", bltnEpochMS),
			"parse_time":    starlark.NewBuiltin("parse_time", bltnParseTime),
			"from_epoch_ms": starlark.NewBuiltin("from_epoch_ms", bltnFromEpochMS),
			"now_ms":        starlark.NewBuiltin("now_ms", bltnNowMS),
		}

		// chương trình "wrap" để ép client định nghĩa run(rows)
		// (Bạn có thể cho phép code thuần, nhưng khuyến cáo dạng hàm)
		globals, err := starlark.ExecFile(thread, "script.star", code, pre)
		if err != nil {
			runErr = fmt.Errorf("compile: %w", err)
			return
		}

		runFnVal, ok := globals["run"]
		if !ok {
			runErr = fmt.Errorf("script must define function: run(rows)")
			return
		}
		runFn, ok := runFnVal.(starlark.Callable)
		if !ok {
			runErr = fmt.Errorf("run is not callable")
			return
		}

		// chuyển rows -> starlark list/dict
		argRows := goRowsToStarlark(rows)
		// gọi run(rows)
		ret, err := starlark.Call(thread, runFn, starlark.Tuple{argRows}, nil)
		if err != nil {
			runErr = fmt.Errorf("runtime: %w", err)
			return
		}
		// convert kết quả về Go
		result = starlarkToGo(ret)
	}()

	select {
	case <-ctx.Done():
		// cố gắng cancel (starlark có hỗ trợ Cancel trong op tốn kém)
		reason := "context canceled"
		if e := ctx.Err(); e != nil {
			reason = e.Error()
		}
		thread.Cancel(reason)

		<-done
		if runErr != nil {
			return nil, runErr
		}
		return nil, fmt.Errorf("script timeout/canceled: %w", ctx.Err())
	case <-done:
		return result, runErr
	}
}

// ---------- Builtins (safe) ----------

func bltnTimeFormat(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var v starlark.Value
	var zone, fmtStr string
	zone = "Asia/Ho_Chi_Minh"
	fmtStr = "02-01-2006 15:04:05.000"
	if err := starlark.UnpackArgs("time_format", args, kwargs,
		"value", &v,
		"tz?", &zone,
		"fmt?", &fmtStr,
	); err != nil {
		return nil, err
	}
	ms, ok := toUnixMilliFromStarlark(v)
	if !ok {
		return starlark.None, nil
	}
	loc, err := time.LoadLocation(zone)
	if err != nil || loc == nil {
		loc = time.FixedZone(zone, 7*3600)
	}
	return starlark.String(time.UnixMilli(ms).In(loc).Format(fmtStr)), nil
}

func bltnToFloat(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var v starlark.Value
	if err := starlark.UnpackArgs("to_float", args, kwargs, "value", &v); err != nil {
		return nil, err
	}
	f, ok := toFloat64FromStarlark(v)
	if !ok || math.IsNaN(f) || math.IsInf(f, 0) {
		return starlark.None, nil
	}
	return starlark.Float(f), nil
}

func bltnToInt(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var v starlark.Value
	if err := starlark.UnpackArgs("to_int", args, kwargs, "value", &v); err != nil {
		return nil, err
	}
	f, ok := toFloat64FromStarlark(v)
	if !ok {
		return starlark.None, nil
	}
	return starlark.MakeInt(int(f)), nil
}

func bltnToStr(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var v starlark.Value
	if err := starlark.UnpackArgs("to_str", args, kwargs, "value", &v); err != nil {
		return nil, err
	}
	return starlark.String(v.String()), nil
}

func bltnEpochMS(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var v starlark.Value
	if err := starlark.UnpackArgs("epoch_ms", args, kwargs, "value", &v); err != nil {
		return nil, err
	}
	ms, ok := toUnixMilliFromStarlark(v)
	if !ok {
		return starlark.None, nil
	}
	return starlark.MakeInt64(ms), nil
}

func bltnParseTime(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var s, layout string
	if err := starlark.UnpackArgs("parse_time", args, kwargs,
		"s", &s,
		"layout?", &layout,
	); err != nil {
		return nil, err
	}
	var t time.Time
	var err error
	if layout == "" {
		// ưu tiên RFC3339Nano
		t, err = time.Parse(time.RFC3339Nano, s)
		if err != nil {
			t, err = time.Parse(time.RFC3339, s)
		}
	} else {
		t, err = time.Parse(layout, s)
	}
	if err != nil {
		return starlark.None, nil
	}
	return starlark.MakeInt64(t.UnixMilli()), nil
}

func bltnFromEpochMS(_ *starlark.Thread, _ *starlark.Builtin, args starlark.Tuple, kwargs []starlark.Tuple) (starlark.Value, error) {
	var ms int64
	var zone string
	zone = "UTC"
	if err := starlark.UnpackArgs("from_epoch_ms", args, kwargs,
		"ms", &ms,
		"tz?", &zone,
	); err != nil {
		return nil, err
	}
	loc, err := time.LoadLocation(zone)
	if err != nil || loc == nil {
		loc = time.UTC
	}
	t := time.UnixMilli(ms).In(loc)
	return starlark.String(t.Format(time.RFC3339Nano)), nil
}

func bltnNowMS(_ *starlark.Thread, _ *starlark.Builtin, _ starlark.Tuple, _ []starlark.Tuple) (starlark.Value, error) {
	return starlark.MakeInt64(time.Now().UnixMilli()), nil
}

// ---------- Convert Go <-> Starlark ----------

func goRowsToStarlark(rows []map[string]any) starlark.Value {
	lst := make([]starlark.Value, 0, len(rows))
	for _, r := range rows {
		d := starlark.NewDict(len(r))
		for k, v := range r {
			d.SetKey(starlark.String(k), goToStarlark(v))
		}
		lst = append(lst, d)
	}
	return starlark.NewList(lst)
}

func goToStarlark(v any) starlark.Value {
	switch x := v.(type) {
	case nil:
		return starlark.None
	case bool:
		if x {
			return starlark.True
		}
		return starlark.False
	case int:
		return starlark.MakeInt(x)
	case int64:
		return starlark.MakeInt64(x)
	case float32:
		return starlark.Float(x)
	case float64:
		return starlark.Float(x)
	case string:
		return starlark.String(x)
	case time.Time:
		return starlark.String(x.Format(time.RFC3339Nano))
	case []any:
		l := make([]starlark.Value, len(x))
		for i, e := range x {
			l[i] = goToStarlark(e)
		}
		return starlark.NewList(l)
	case map[string]any:
		d := starlark.NewDict(len(x))
		for k, v := range x {
			d.SetKey(starlark.String(k), goToStarlark(v))
		}
		return d
	default:
		// fallback
		return starlark.String(fmt.Sprint(x))
	}
}

func starlarkToGo(v starlark.Value) any {
	switch x := v.(type) {
	case starlark.NoneType:
		return nil
	case starlark.Bool:
		return bool(x)
	case starlark.Int:
		i, _ := x.Int64()
		return i
	case starlark.Float:
		return float64(x)
	case starlark.String:
		return string(x)
	case *starlark.List:
		out := make([]any, x.Len())
		iter := x.Iterate()
		defer iter.Done()
		var item starlark.Value
		i := 0
		for iter.Next(&item) {
			out[i] = starlarkToGo(item)
			i++
		}
		return out
	case *starlark.Dict:
		out := make(map[string]any, x.Len())
		for _, t := range x.Items() {
			k := t[0]
			v := t[1]
			out[fmt.Sprint(k)] = starlarkToGo(v)
		}
		return out
	default:
		return x.String()
	}
}

func toUnixMilliFromStarlark(v starlark.Value) (int64, bool) {
	switch t := v.(type) {
	case starlark.Int:
		i, ok := t.Int64()
		if !ok {
			return 0, false
		}
		// đoán đơn vị theo độ dài chuỗi
		s := t.String()
		digits := len(strings.TrimLeft(s, "-+"))
		switch {
		case digits <= 11:
			return i * 1000, true // sec
		case digits <= 13:
			return i, true // ms
		case digits <= 16:
			return i / 1000, true // micro -> ms
		default:
			return i / 1_000_000, true // nano -> ms
		}
	case starlark.Float:
		f := float64(t)
		return int64(f), true
	case starlark.String:
		str := string(t)
		// thử RFC3339(Nano)
		if tm, err := time.Parse(time.RFC3339Nano, str); err == nil {
			return tm.UnixMilli(), true
		}
		// cố gắng parse layout phổ biến "2006-01-02 15:04:05"
		if tm, err := time.Parse("2006-01-02 15:04:05", str); err == nil {
			return tm.UnixMilli(), true
		}
		// số dạng chuỗi
		if n, err := strconv.ParseInt(strings.TrimSpace(str), 10, 64); err == nil {
			return n, true
		}
		return 0, false
	default:
		return 0, false
	}
}

func toFloat64FromStarlark(v starlark.Value) (float64, bool) {
	switch t := v.(type) {
	case starlark.Float:
		return float64(t), true
	case starlark.Int:
		i, _ := t.Int64()
		return float64(i), true
	case starlark.String:
		f, err := strconv.ParseFloat(strings.TrimSpace(string(t)), 64)
		return f, err == nil
	default:
		return math.NaN(), false
	}
}
