package supportsvc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// Querier: trừu tượng hoá lớp truy vấn Influx (đã có adapter ở main)
type Querier interface {
	Query(ctx context.Context, org, bucket, flux string) ([]map[string]any, error)
	Close() error
}

type DatabaseHandler struct {
	MQTT          mqtt.Client
	QoS           byte
	Retain        bool
	DefaultOrg    string
	DefaultBucket string
	Querier       Querier

	// Không giới hạn: để 0 => không timeout, không cắt rows
	ScriptTimeout time.Duration
	MaxRows       int
}

func (h *DatabaseHandler) Kind() string { return "database" }

type DBPayload struct {
	Action        string      `json:"action"`
	Org           string      `json:"org,omitempty"`
	Bucket        string      `json:"bucket,omitempty"`
	Query         string      `json:"query,omitempty"`
	Script        *ScriptSpec `json:"script,omitempty"`
	ResponseTopic string      `json:"responseTopic,omitempty"`
	CorrelationID string      `json:"correlationId,omitempty"`
	Output        string      `json:"output,omitempty"` // back-compat
}

type ScriptSpec struct {
	Lang string `json:"lang"` // "starlark"
	Code string `json:"code"` // phải định nghĩa run(rows) hoặc có thể trả trực tiếp
}

func (h *DatabaseHandler) Handle(ctx context.Context, raw json.RawMessage) error {
	var p DBPayload
	if err := json.Unmarshal(raw, &p); err != nil {
		return fmt.Errorf("database payload invalid: %w", err)
	}
	if p.Action == "" {
		p.Action = "query"
	}
	if !strings.EqualFold(p.Action, "query") {
		return fmt.Errorf("unsupported action: %s", p.Action)
	}

	org := p.Org
	if org == "" {
		org = h.DefaultOrg
	}
	bucket := p.Bucket
	if bucket == "" {
		bucket = h.DefaultBucket
	}
	if strings.TrimSpace(p.Query) == "" {
		return errors.New("missing query")
	}

	rows, err := h.Querier.Query(ctx, org, bucket, p.Query)
	if err != nil {
		return h.publishError(p, fmt.Errorf("query error: %w", err))
	}

	// Không giới hạn: không cắt rows (trừ khi MaxRows>0 được set thủ công)
	if h.MaxRows > 0 && len(rows) > h.MaxRows {
		rows = rows[:h.MaxRows]
	}

	var result any = rows
	if p.Script != nil {
		if !strings.EqualFold(p.Script.Lang, "starlark") {
			return h.publishError(p, fmt.Errorf("unsupported script.lang: %s", p.Script.Lang))
		}
		// ScriptTimeout==0 => không đặt timeout; chỉ huỷ khi ctx Done
		res, sErr := runStarlarkTransform(ctx, p.Script.Code, rows, h.ScriptTimeout)
		if sErr != nil {
			return h.publishError(p, fmt.Errorf("script error: %w", sErr))
		}
		result = res
	}

	resp := map[string]any{"ok": true, "correlationId": p.CorrelationID, "result": result}
	buf, _ := json.Marshal(resp)
	topic := p.ResponseTopic
	if topic == "" {
		topic = p.Output
	}
	if topic == "" {
		topic = "SystemSupport/Response"
	}
	tok := h.MQTT.Publish(topic, h.QoS, h.Retain, buf)
	tok.Wait()
	if err := tok.Error(); err != nil {
		log.Printf("[DatabaseHandler] publish error: %v", err)
		return err
	}
	return nil
}

func (h *DatabaseHandler) publishError(p DBPayload, err error) error {
	resp := map[string]any{"ok": false, "correlationId": p.CorrelationID, "error": err.Error()}
	buf, _ := json.Marshal(resp)
	topic := p.ResponseTopic
	if topic == "" {
		topic = p.Output
	}
	if topic == "" {
		topic = "SystemSupport/Response"
	}
	tok := h.MQTT.Publish(topic, h.QoS, h.Retain, buf)
	tok.Wait()
	return err
}
